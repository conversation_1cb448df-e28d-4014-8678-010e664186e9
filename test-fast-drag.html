<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速拖拽修复测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .test-container {
            border: 2px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
        }
        
        .drag-area {
            width: 100%;
            height: 400px;
            border: 2px dashed #999;
            position: relative;
            background: #fafafa;
            overflow: hidden;
        }
        
        .iframe-container {
            width: 100%;
            height: 300px;
            border: 1px solid #999;
            position: relative;
            background: #e8f4fd;
        }
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .draggable-note {
            position: absolute;
            background: #ffeb3b;
            border: 2px solid #fbc02d;
            padding: 15px;
            border-radius: 8px;
            cursor: move;
            user-select: none;
            z-index: 1000;
            min-width: 200px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: box-shadow 0.2s ease;
        }
        
        .draggable-note:hover {
            box-shadow: 0 6px 16px rgba(0,0,0,0.2);
        }
        
        .draggable-note.dragging {
            box-shadow: 0 8px 20px rgba(0,0,0,0.3);
            transform: rotate(2deg);
        }
        
        .drag-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 999999;
            background: transparent;
            cursor: move;
            pointer-events: auto;
        }
        
        .debug-info {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            border-left: 4px solid #2196f3;
        }
        
        .test-button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #1565c0;
        }
        
        .speed-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <h1>快速拖拽修复测试</h1>
    
    <div class="instructions">
        <h3>测试说明：</h3>
        <ul>
            <li>尝试快速拖拽便签，让鼠标移动到iframe区域</li>
            <li>观察便签是否能继续跟随鼠标移动</li>
            <li>测试在不同速度下的拖拽体验</li>
            <li>检查拖拽结束后是否正确清理</li>
        </ul>
    </div>
    
    <div class="test-container">
        <h2>测试1: 普通拖拽区域</h2>
        <div class="drag-area" id="normal-area">
            <div class="draggable-note" id="note1" style="top: 50px; left: 50px;">
                <strong>便签1</strong><br>
                快速拖拽我到iframe区域试试！
            </div>
            <div class="draggable-note" id="note2" style="top: 150px; left: 250px;">
                <strong>便签2</strong><br>
                我也可以快速拖拽哦！
            </div>
        </div>
        <div class="debug-info" id="normal-debug">等待拖拽测试...</div>
    </div>
    
    <div class="test-container">
        <h2>测试2: 包含iframe的拖拽区域</h2>
        <div class="drag-area" id="iframe-area">
            <div class="draggable-note" id="note3" style="top: 20px; left: 20px;">
                <strong>便签3</strong><br>
                拖拽我穿越iframe！
            </div>
            <div class="iframe-container" style="position: absolute; top: 100px; left: 200px; width: 300px; height: 200px;">
                <iframe src="data:text/html,
                    <html>
                    <head><title>iframe测试区域</title></head>
                    <body style='margin:0;padding:20px;background:#e8f4fd;'>
                        <h3>iframe区域</h3>
                        <p>这是iframe内容区域</p>
                        <p>拖拽便签经过这里时应该继续工作</p>
                    </body>
                    </html>
                "></iframe>
            </div>
        </div>
        <div class="debug-info" id="iframe-debug">等待拖拽测试...</div>
    </div>

    <script>
        let dragData = {
            isDragging: false,
            startTime: 0,
            lastX: 0,
            lastY: 0,
            speed: 0,
            element: null,
            overlay: null
        };

        function createDragOverlay(noteId) {
            // 移除可能存在的旧遮罩
            const oldOverlay = document.getElementById(`drag-overlay-${noteId}`);
            if (oldOverlay) {
                oldOverlay.remove();
            }

            // 创建新的拖拽遮罩
            const overlay = document.createElement('div');
            overlay.id = `drag-overlay-${noteId}`;
            overlay.className = 'drag-overlay';
            document.body.appendChild(overlay);
            return overlay;
        }

        function removeDragOverlay(noteId) {
            const overlay = document.getElementById(`drag-overlay-${noteId}`);
            if (overlay) {
                overlay.remove();
            }
        }

        function calculateSpeed(x, y, time) {
            if (dragData.lastX !== 0 && dragData.lastY !== 0) {
                const deltaX = x - dragData.lastX;
                const deltaY = y - dragData.lastY;
                const deltaTime = time - dragData.startTime;
                const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
                return deltaTime > 0 ? (distance / deltaTime * 1000).toFixed(0) : 0; // pixels per second
            }
            return 0;
        }

        function updateDebugInfo(noteId, info) {
            const debugElement = document.getElementById(noteId.includes('3') ? 'iframe-debug' : 'normal-debug');
            debugElement.innerHTML = `
                <strong>拖拽状态:</strong> ${info.isDragging ? '拖拽中' : '未拖拽'}<br>
                <strong>鼠标速度:</strong> ${info.speed} px/s<br>
                <strong>当前位置:</strong> (${info.x}, ${info.y})<br>
                <strong>遮罩状态:</strong> ${info.hasOverlay ? '已创建' : '未创建'}<br>
                <strong>时间:</strong> ${new Date().toLocaleTimeString()}
            `;
        }

        function initDraggable(element) {
            let startX = 0, startY = 0;
            let currentX = 0, currentY = 0;
            let initialMouseX = 0, initialMouseY = 0;

            element.addEventListener('mousedown', function(e) {
                dragData.isDragging = true;
                dragData.startTime = Date.now();
                dragData.element = element;
                dragData.lastX = e.clientX;
                dragData.lastY = e.clientY;

                // 获取当前位置
                const rect = element.getBoundingClientRect();
                startX = rect.left;
                startY = rect.top;
                initialMouseX = e.clientX;
                initialMouseY = e.clientY;

                // 设置全局样式
                document.body.style.userSelect = 'none';
                document.body.style.pointerEvents = 'none';
                element.style.pointerEvents = 'auto';
                element.classList.add('dragging');

                // 创建拖拽遮罩
                dragData.overlay = createDragOverlay(element.id);

                // 添加全局鼠标事件监听器
                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);

                e.preventDefault();
            });

            function handleMouseMove(e) {
                if (!dragData.isDragging) return;

                const currentTime = Date.now();
                dragData.speed = calculateSpeed(e.clientX, e.clientY, currentTime);
                dragData.lastX = e.clientX;
                dragData.lastY = e.clientY;

                // 计算新位置
                currentX = startX + (e.clientX - initialMouseX);
                currentY = startY + (e.clientY - initialMouseY);

                // 边界限制
                const maxX = window.innerWidth - element.offsetWidth;
                const maxY = window.innerHeight - element.offsetHeight;
                currentX = Math.max(0, Math.min(currentX, maxX));
                currentY = Math.max(0, Math.min(currentY, maxY));

                // 更新位置
                element.style.left = currentX + 'px';
                element.style.top = currentY + 'px';

                // 更新调试信息
                updateDebugInfo(element.id, {
                    isDragging: true,
                    speed: dragData.speed,
                    x: Math.round(currentX),
                    y: Math.round(currentY),
                    hasOverlay: !!dragData.overlay
                });
            }

            function handleMouseUp(e) {
                if (!dragData.isDragging) return;

                // 清理状态
                dragData.isDragging = false;
                document.body.style.userSelect = '';
                document.body.style.pointerEvents = '';
                element.style.pointerEvents = '';
                element.classList.remove('dragging');

                // 移除拖拽遮罩
                removeDragOverlay(element.id);
                dragData.overlay = null;

                // 移除全局事件监听器
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);

                // 更新调试信息
                updateDebugInfo(element.id, {
                    isDragging: false,
                    speed: 0,
                    x: Math.round(currentX),
                    y: Math.round(currentY),
                    hasOverlay: false
                });
            }
        }

        // 初始化所有可拖拽元素
        document.addEventListener('DOMContentLoaded', function() {
            const draggableElements = document.querySelectorAll('.draggable-note');
            draggableElements.forEach(initDraggable);

            // 初始化调试信息
            updateDebugInfo('note1', {
                isDragging: false,
                speed: 0,
                x: 50,
                y: 50,
                hasOverlay: false
            });
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            document.querySelectorAll('[id^="drag-overlay-"]').forEach(overlay => {
                overlay.remove();
            });
        });
    </script>
</body>
</html>
