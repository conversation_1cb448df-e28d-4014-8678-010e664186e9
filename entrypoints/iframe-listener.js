import { defineContentScript } from "wxt/sandbox";

export default defineContentScript({
  matches: ["<all_urls>"],
  runAt: "document_idle",
  allFrames: true, // 让它能注入到 iframe
  main(ctx) {
    if (window.top !== window) {
      // document.addEventListener("mouseup", () => {
      //   console.log("22333333")
      //   const selection = window.getSelection();
      //   if (!selection || selection.isCollapsed) return;

      //   const range = selection.getRangeAt(0);
      //   const rect = range.getBoundingClientRect();

      //   window.top?.postMessage(
      //     {
      //       type: "iframe-selection",
      //       text: selection.toString(),
      //       rect: {
      //         top: rect.top,
      //         left: rect.left,
      //         width: rect.width,
      //         height: rect.height,
      //       },
      //     },
      //     "*",
      //   );
      // });
      // document.addEventListener("mouseup", (e) => {
      //   const target = e.target;
      //   if (target && (target.tagName === "INPUT" || target.tagName === "TEXTAREA")) {
      //     const el = target
      //     const start = el.selectionStart ?? 0;
      //     const end = el.selectionEnd ?? 0;
      //     const text = el.value.substring(start, end);
      //     console.log(text, 1231)
      //     if (text) {
      //       const rect = el.getBoundingClientRect();
      //       window.top?.postMessage(
      //         {
      //           type: "iframe-input-selection",
      //           text,
      //           rect: {
      //             top: rect.top,
      //             left: rect.left,
      //             width: rect.width,
      //             height: rect.height,
      //           },
      //         },
      //         "*",
      //       );
      //     }
      //   }
      // });

      document.addEventListener("pointerup", () => {
        setTimeout(() => {
          const selection = window.getSelection();
          if (!selection || selection.isCollapsed) return;

          const text = selection.toString().trim();
          if (!text) return;

          const range = selection.getRangeAt(0);
          const rect = range.getBoundingClientRect();

          window.top.postMessage(
            {
              type: "iframe-selection",
              text,
              rect: {
                top: rect.top,
                left: rect.left,
                width: rect.width,
                height: rect.height,
              },
            }, "*");
        }, 10); // 微延迟确保 Range 已更新
      });
      document.addEventListener("pointerdown", (e) => {
        console.log(222)
        // 点击任何地方都通知顶层
        window.parent.postMessage({ type: "iframe-mousedown" }, "*");
      });

    }
  },
});
