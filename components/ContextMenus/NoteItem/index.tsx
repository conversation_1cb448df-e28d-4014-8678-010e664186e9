import React, { useEffect, useRef, useState } from "react";
import interact from "interactjs";
import { useGetState } from "ahooks";
import {
  AI,
  QUIZ,
  ONQUIZ,
  NOTE_DETAIL_COOPERATION,
  NOTE_MODE,
  NOTE_THEME_PICKER_HEIGHT,
  PLAIN,
  QUOTE,
  reRenderNote,
  scrollToNoteBase,
  setNoteShowClose,
  updateParentPosition,
} from "@/utils/notes";
import NotePin from "./NotePin";
import CopyUrl from "../ConfigPanel/CopyUrl/index";
import ThemePicker from "../ConfigPanel/ThemePicker/index";
import Comment from "../ConfigPanel/Comment/index";
import Delete from "../ConfigPanel/Delete/index";
import PlainNote from "../ConfigPanel/PlainNote/index";
import { CooperationSVGIcon, CopySVGIcon, InfoSVGIcon, ThemeSVGIcon } from "@/config/menu/note";
import { SHADOW_SIDE_PANEL, SIDE_PANEL_WRAPPER_ID } from "@/entrypoints/content/sidepanel";
import setModifyItem, {
  NOTE_DETAIL_STORAGE_KEY,
  NOTE_MODIFY_STORAGE_KEY,
  SET_PLACE_TOP_ID,
} from "@/utils/browserStorageCurrentPage";
import NoteIns from "@/components/ContextMenus/ConfigPanel/NoteIns/index";
import { Button, Dropdown, Flex, MenuProps, message, theme, Tooltip } from "antd";
import classNames from "classnames";
import { SendByPrompt } from "@/types/chat";
import NoteSelectGroupModal from "@/components/NoteGroupModal/noteSelectGroup";
import "./index.less";
import { MedicineBoxFilled, MedicineBoxOutlined } from "@ant-design/icons";
import IconFont from "@/components/IconFont";
import NoteKnowledgeModal from "@/components/NoteGroupModal/noteKnowledge";
import NoteGroupModal from "@/components/NoteGroupModal";

const { useToken } = theme;

const NoteItem: React.FC<{
  note: CopilotNote;
  isThumbnail?: boolean;
  isHover: boolean;
  style?: React.CSSProperties;
}> = ({ note, isThumbnail = false, isHover = false, style = {} }) => {
  // 元素 resize 或 draggle 时，只要距浏览器边缘有一定距离，就不会导致临近边界时网页出现滚动条
  // 悬浮框绝对或固定定位，左右上下的定位值在网页范围内，并且避免将悬浮框拖拽到网页边缘
  const { token } = useToken();
  // 便签容器class 名称
  const NOTE_PANEL_NAME = "sino-note-info";
  // 便签悬浮设置容器 class 名称
  const NOTE_CONFIG_PANEL = "sino-config-panel";
  const [noteInfo, setNoteInfo, getNoteInfo] = useGetState(note);
  const [placeTop, setPlaceTop] = useState("");
  const [visibility, setVisibility] = useState<"visible" | "hidden">("hidden");
  const [configPos, setConfigPos] = useState("left"); // 分享链接位于便签的左边还是右边
  const [barKey, setBarKey] = useState("");
  const [themePanelTop, setThemePanelTop] = useState(0);
  const fetchRequest = useFetchRequest();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false); // 是否打开Dropdown
  const isClickInThisRef = useRef(false); // 是否点击的当前元素
  const isDraggingRef = useRef(false); // 标记拖拽状态
  const dropdownOpenRef = useRef(false); // 是否打开Dropdown
  const [groupVisible, setGroupVisible] = useState(false); // 保存到组
  const [addGroupVisible, setAddGroupVisible] = useState(false); // 新增便签组
  const [modalVisible, setModalVisible] = useState(false); // 知识库
  const [noteInfoData, setNoteInfoData] = useState<any>(); // 知识库弹框数据

  useEffect(() => {
    setTimeout(() => {
      setVisibility("visible");
    }, 900);
  }, []);

  useEffect(() => {
    let sinoKey = SET_PLACE_TOP_ID + sessionStorage.getItem("sino-tap-key");
    const placeTopIdChangeEvent = (changes) => {
      let changeId = changes[sinoKey];
      if (changeId && changeId.newValue) {
        setPlaceTop(changeId.newValue);
        browser.storage.local.remove(sinoKey);
      }
    };
    browser.storage.local.onChanged.addListener(placeTopIdChangeEvent);
    return () => {
      browser.storage.local.onChanged.removeListener(placeTopIdChangeEvent);
    };
  }, []);

  useEffect(() => {
    setNoteInfo(note);
  }, [note]);
  useEffect(() => {
    const shadowDomDiv = document.getElementById(SHADOW_SIDE_PANEL).shadowRoot;
    const div = shadowDomDiv.querySelector<HTMLDivElement>(`#${SIDE_PANEL_WRAPPER_ID}`);
    const startDrag = (input: HTMLElement, bodyWidth: number, scrollHeight: number) => {
      interact(input.querySelector("." + NOTE_PANEL_NAME) as HTMLElement)
        // 拖拽动作
        .draggable({
          listeners: {
            start(event) {
              // 拖拽开始时设置全局样式，防止文本选择
              document.body.style.userSelect = "none";
              div.style.userSelect = "none";
              document.body.style.pointerEvents = "none"; // 防止iframe干扰
              input.style.pointerEvents = "auto"; // 保持拖拽元素可交互
              isDraggingRef.current = true;

              // 创建一个全屏的透明遮罩来捕获鼠标事件
              const dragOverlay = document.createElement("div");
              dragOverlay.id = `drag-overlay-${noteInfo.id}`;
              dragOverlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                z-index: 2147483646;
                background: transparent;
                cursor: move;
                pointer-events: auto;
              `;
              document.body.appendChild(dragOverlay);
            },
            move(event) {
              isDraggingRef.current = true;
              let x = (parseFloat(input.getAttribute("data-x")) || 0) + event.delta.x;
              let y = (parseFloat(input.getAttribute("data-y")) || 0) + event.delta.y;
              if (getNoteInfo().noteStyle.noteType === NOTE_MODE) {
                let tag = {
                  target: [],
                  coordinates: {
                    left: 0,
                    top: 0,
                  },
                };
                try {
                  tag = JSON.parse(getNoteInfo().tag) || {};
                } catch (error) {
                  console.error("获取或解析 noteList 时发生错误:", error);
                }
                input.style.left = `${tag.coordinates?.left || 0}px`;
                input.style.top = `${tag.coordinates?.top || 0}px`;
              } else {
                input.style.left = "0px";
                input.style.top = "0px";
              }
              input.style.transform = `translate(${x}px, ${y}px)`;
              input.setAttribute("data-x", x);
              input.setAttribute("data-y", y);
              // 拖拽过程中，悬浮框隐藏
              barKey && setBarKey("");
            },
            end() {
              // 恢复全局样式
              document.body.style.userSelect = "";
              div.style.userSelect = "";
              document.body.style.pointerEvents = "";
              input.style.pointerEvents = "";

              // 移除拖拽遮罩
              const dragOverlay = document.getElementById(`drag-overlay-${noteInfo.id}`);
              if (dragOverlay) {
                dragOverlay.remove();
              }

              let noteInfo = getNoteInfo();
              let tag = {
                target: [],
                coordinates: {
                  left: 0,
                  top: 0,
                },
              };
              try {
                tag = JSON.parse(noteInfo.tag) || {};
              } catch (error) {
                console.error("获取或解析 noteList 时发生错误:", error);
              }
              const maxWidth =
                bodyWidth -
                input.offsetWidth -
                (noteInfo.noteStyle.noteType === NOTE_MODE ? tag.coordinates?.left || 0 : 0);
              const maxHeight =
                (noteInfo.noteStyle.noteType === NOTE_MODE ? scrollHeight : window.innerHeight) -
                (noteInfo.noteStyle.noteType === NOTE_MODE ? tag.coordinates?.top || 0 : 0) -
                input.offsetHeight;
              const minHeight = noteInfo.noteStyle.noteType === NOTE_MODE ? -input.style.top : 0;
              const minWidth = noteInfo.noteStyle.noteType === NOTE_MODE ? -input.style.left : 0;
              dragEnd(input, minWidth, maxWidth, minHeight, maxHeight);
            },
          },
          inertia: true,
          // 增强拖拽体验的配置
          cursorChecker: () => "move", // 始终显示移动光标
          allowFrom: `.${NOTE_PANEL_NAME}`, // 只允许从便签面板开始拖拽
          ignoreFrom: "input, textarea, button, select, option", // 忽略这些元素的拖拽
          modifiers: [
            interact.modifiers.restrictRect({
              restriction: {
                top: 0,
                left: 0,
                right: Math.max(bodyWidth, 300), // 确保最小宽度，防止office文档环境下的异常值
                bottom: Math.max(scrollHeight - input.offsetHeight, 200), // 确保最小高度
              },
              // endOnly: true, // 仅在结束时进行限制
            }),
          ],
        });
    };
    const dragEnd = (input, minWidth, maxWidth, minHeight, maxHeight) => {
      let finalX = parseFloat(input.getAttribute("data-x"));
      let finalY = parseFloat(input.getAttribute("data-y"));

      // 检查是否有异常的坐标值（可能由office文档环境导致）
      if (isNaN(finalX) || finalX < -20000 || finalX > 20000) {
        finalX = 0; // 重置为安全位置
      }
      if (isNaN(finalY) || finalY < -20000 || finalY > 20000) {
        finalY = 0; // 重置为安全位置
      }

      if (finalX < minWidth) finalX = minWidth;
      if (finalX > maxWidth) finalX = maxWidth;

      // 限制范围：不超出 body 的高度
      if (finalY < minHeight) finalY = minHeight;
      if (finalY > maxHeight) finalY = maxHeight;

      input.style.transform = `translate(${finalX}px, ${finalY}px)`;
      input.setAttribute("data-x", finalX.toString());
      input.setAttribute("data-y", finalY.toString());

      setNoteInfo(() => {
        let newState = {
          ...getNoteInfo(),
          noteStyle: {
            ...getNoteInfo().noteStyle,
            noteTop: finalY,
            noteLeft: finalX,
          },
        };
        if (noteInfo.type !== QUIZ && noteInfo.type !== AI) {
          editNoteRequest(newState);
        }
        return newState;
      });
    };
    // 便签拖拽监听事件
    const startMove = async (e: MouseEvent) => {
      isClickInThisRef.current = false;
      const ele: any = e.target;
      let dom = document.getElementById("shadow-dom-note");
      if (dom) {
        const shadowDom = dom.shadowRoot;
        const input = shadowDom.getElementById(`note${noteInfo.id}`);
        const clickImg = shadowDom.getElementById(`sino-click-img-${noteInfo.id}`);
        const note_panel = input && input.querySelector("." + NOTE_PANEL_NAME);
        if (!note_panel) return;
        // 在input点击
        if (clickImg.contains(ele)) {
          isClickInThisRef.current = true;
        }
      }
      e.stopPropagation();
    };

    // 判断是否为点击事件
    const handleMouseUp = () => {
      if (isClickInThisRef.current && !isDraggingRef.current) {
        dropdownOpenRef.current = !dropdownOpenRef.current;
        setIsDropdownOpen(dropdownOpenRef.current);
      }
      isDraggingRef.current = false;
    };
    let dom = document.getElementById("shadow-dom-note");
    let shadowDom;
    if (dom) {
      shadowDom = dom.shadowRoot;
      const input = shadowDom.getElementById(`note${noteInfo.id}`);
      if (!input) return;

      // 获取可用的拖拽区域尺寸
      const getDragBounds = () => {
        const bodyRect = document.body.getBoundingClientRect();
        let bodyWidth = bodyRect.width;
        let scrollHeight = bodyRect.height;

        // 检测是否在iframe中的office文档环境
        // 当body的getBoundingClientRect返回异常值时，使用备选方案
        const isInvalidBounds = bodyWidth <= 0 || scrollHeight <= 0 || bodyRect.top < -20000 || bodyRect.left < -20000;

        // 调试日志，帮助诊断office文档环境问题
        if (isInvalidBounds) {
          console.warn("检测到异常的body边界值:", {
            bodyWidth,
            scrollHeight,
            bodyRect,
            isIframe: window.top !== window,
            userAgent: navigator.userAgent,
          });
        }

        if (isInvalidBounds || window.top !== window) {
          // 在iframe环境或异常边界时，使用视窗尺寸
          bodyWidth = Math.max(
            window.innerWidth,
            document.documentElement.clientWidth || 0,
            document.body.clientWidth || 0,
          );
          scrollHeight = Math.max(
            window.innerHeight,
            document.documentElement.clientHeight || 0,
            document.body.clientHeight || 0,
            document.documentElement.scrollHeight || 0,
            document.body.scrollHeight || 0,
          );

          console.log("使用备选边界值:", { bodyWidth, scrollHeight });
        }

        return { bodyWidth, scrollHeight };
      };

      const { bodyWidth, scrollHeight } = getDragBounds();

      // 检查并修复便签的初始位置，防止office文档环境导致的异常坐标
      const currentX = parseFloat(input.getAttribute("data-x")) || 0;
      const currentY = parseFloat(input.getAttribute("data-y")) || 0;

      if (
        isNaN(currentX) ||
        currentX < -20000 ||
        currentX > 20000 ||
        isNaN(currentY) ||
        currentY < -20000 ||
        currentY > 20000
      ) {
        console.warn("检测到异常的便签坐标，重置为安全位置:", { currentX, currentY });
        input.setAttribute("data-x", "0");
        input.setAttribute("data-y", "0");
        input.style.transform = "translate(0px, 0px)";
      }

      startDrag(input, bodyWidth, scrollHeight);
      shadowDom.addEventListener("mousedown", startMove);
      shadowDom.addEventListener("mouseup", handleMouseUp);
    }
    return () => {
      shadowDom && shadowDom.removeEventListener("mousedown", startMove);
      shadowDom && shadowDom.removeEventListener("mouseup", handleMouseUp);

      // 清理可能残留的拖拽遮罩
      const dragOverlay = document.getElementById(`drag-overlay-${noteInfo.id}`);
      if (dragOverlay) {
        dragOverlay.remove();
      }

      // 恢复全局样式（防止异常情况下样式未恢复）
      document.body.style.userSelect = "";
      document.body.style.pointerEvents = "";
    };
  }, [noteInfo]);

  useEffect(() => {
    const outSideNoteWrapper = (e) => {
      e.stopPropagation(); // 阻止事件继续传播
      const ele = e.target;
      let dom = document.getElementById("shadow-dom-note");
      if (dom) {
        const shadowDom = dom.shadowRoot;
        const div = shadowDom.getElementById(`note${noteInfo.id}`);
        // 点击空白处关闭 换肤、协同等
        if (div && !div.contains(ele)) {
          setBarKey("");
          setPlaceTop("");
        }
      }
    };
    document.addEventListener("mousedown", outSideNoteWrapper);
    return () => {
      document.removeEventListener("mousedown", outSideNoteWrapper);
    };
  }, []);

  const selfUpdateNoteInfo = (changeObj, type?) => {
    setNoteInfo((prevNoteInfo) => {
      let updatedNoteInfo: any = {};
      if (type) {
        let obj = {
          ...prevNoteInfo[type],
          ...changeObj[type],
        };
        changeObj[type] = obj;
        updatedNoteInfo = {
          ...prevNoteInfo,
          ...changeObj, // 假设这里是最新的便签数据
        };
      } else {
        updatedNoteInfo = {
          ...prevNoteInfo,
          ...changeObj, // 假设这里是最新的便签数据
        };
      }
      if (updatedNoteInfo.editable) {
        editNoteRequest(updatedNoteInfo);
      } else {
        setModifyItem(NOTE_MODIFY_STORAGE_KEY, { ...updatedNoteInfo, key: new Date().getTime(), updateType: "edit" });
      }
      switch (type) {
        case "changeFixed":
          reRenderNote(updatedNoteInfo);
          break;

        default:
          break;
      }
      return updatedNoteInfo; // 返回新的状态
    });
  };

  const addNoteRequest = (noteElement) => {
    fetchRequest({
      api: "addNote",
      params: noteElement,
      callback: async (res) => {
        if (res.code === 200) {
          let note = {
            ...res.data,
            displayFlag: 1,
            promptId: noteElement.promptId,
            promptTitle: noteElement.promptTitle,
            promptContent: noteElement.promptContent,
          };
          if (noteElement.promptId || noteElement.promptId === 0) {
            await sendByPrompt({
              noteId: res.data.id,
              promptId: noteElement.promptId,
              promptTitle: noteElement.promptTitle,
              promptContent: noteElement.promptContent,
            });
          }
          setModifyItem(NOTE_MODIFY_STORAGE_KEY, { ...note, key: new Date().getTime(), updateType: "add" });
          window.postMessage({ type: "setNoteNotice", note }, "*");
          message.open({
            type: "success",
            content: "复制成功",
          });
        } else {
          // 创建报错了给出提示，便签不渲染
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };

  const sendByPrompt = (prompt: SendByPrompt) => {
    fetchRequest({
      api: "addNotePromptRela",
      params: prompt,
      callback: (res) => {
        if (res.code !== 200) {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };

  // 便签修改接口请求，重新存储 位置信息
  const editNoteRequest = (noteElement: CopilotNote) => {
    fetchRequest({
      api: "editNote",
      params: noteElement,
      callback: (res) => {
        if (res.code === 200) {
          setModifyItem(NOTE_MODIFY_STORAGE_KEY, { ...noteElement, key: new Date().getTime(), updateType: "edit" });
          // browser.storage.local.set({
          //   // 这个是更新数据变化，如果更新了，便签组数据要跟着改变，但是这个下一版要删掉
          //   uploadGroupData: {
          //     date: Date.now(),
          //     type: "edit",
          //   },
          // });
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };

  // 收起状态
  const changeExpanded = (type: boolean) => {
    selfUpdateNoteInfo({ displayFlag: type ? 1 : 0 });
    setNoteShowClose(noteInfo.id, type);
  };

  const changeFixed = (selfFixed) => {
    let notePanelRect = { left: 0, top: 0 };
    let dom = document.getElementById("shadow-dom-note");
    if (dom) {
      const shadowDom = dom.shadowRoot;
      const notePanel = shadowDom.getElementById(`note${noteInfo.id}`);
      notePanelRect = notePanel.getBoundingClientRect();
    }
    let newNote = {
      displayFlag: 1,
      coordinates: noteInfo.coordinates,
      tag: noteInfo.tag,
      noteStyle: { ...noteInfo.noteStyle, noteType: selfFixed ? "absolute" : "fixed", noteLeft: 0, noteTop: 0 },
    };

    if (selfFixed) {
      // 如果不存在页面中的划词，则更新页面中自己创建的依赖dom位置
      if (note.tag.includes("空白元素")) {
        // 更新 note 对象的 coordinates 属性
        newNote.coordinates = {
          ...note.coordinates,
          left: notePanelRect.left,
          top: notePanelRect.top + window.scrollY,
        };
        // 更新 tag 中的 coordinates 值
        const tagObj = JSON.parse(note.tag);
        tagObj.coordinates = { ...newNote.coordinates };
        newNote.tag = JSON.stringify(tagObj);
        updateParentPosition(noteInfo.id, newNote.coordinates.top, newNote.coordinates.left);
      } else {
        scrollToNoteBase(noteInfo.id);
      }
    } else {
      newNote.noteStyle.noteLeft = notePanelRect.left;
      newNote.noteStyle.noteTop = notePanelRect.top;
    }
    selfUpdateNoteInfo(newNote, "changeFixed");
    setPlaceTop("");
  };

  // 便签右侧点击事件，计算悬浮框位置：右 → 左
  const noteSiderClick = (event, key) => {
    // 阻止冒泡到容器拖拽事件
    event.stopPropagation();
    // if (key != "group" && key != "addGroup") {
    //   event.stopPropagation();
    // }
    if (barKey && barKey === key) {
      setBarKey("");
      return;
    }
    let top;
    const notePanelTop = noteInfo.noteStyle.noteTop;
    switch (key) {
      case "theme":
        if (notePanelTop >= NOTE_THEME_PICKER_HEIGHT) {
          top = -83;
        } else {
          top = 40;
        }
        setThemePanelTop(top);
        break;
      case "copy":
        noteInfo.noteStyle.noteLeft += 10;
        noteInfo.noteStyle.noteTop += 10;
        addNoteRequest(noteInfo);
        break;
      case "info":
        setModifyItem(NOTE_DETAIL_STORAGE_KEY, {
          key: new Date().getTime(),
          note: noteInfo,
          expanded: NOTE_DETAIL_COOPERATION,
        });
        break;
      case "addGroup":
        setAddGroupVisible(true);
        break;
      case "group":
        setGroupVisible(true);
        break;
      case "knowlwdge":
        setNoteInfoData({
          title: noteInfo.quoteContent,
          content: noteInfo.content,
        });
        setModalVisible(true);
        break;

      default:
        break;
    }
    setBarKey(key);
  };

  // 主题色
  const handleThemeBack = (changeObj) => {
    setBarKey("");
    selfUpdateNoteInfo(changeObj);
  };

  const handleCooperation = () => {
    setBarKey("");
  };

  // 编辑器文本更新
  const updateNoteValue = (arr) => {
    let obj = {
      noteObjs: [],
    };
    arr.forEach(({ value, key }) => {
      // 创建一个新的DOM元素
      var parser = new DOMParser();
      var doc = parser.parseFromString(value, "text/html");
      var aList = doc.getElementsByTagName("a");
      const dataValues = Array.from(aList).map((anchor) => JSON.parse(anchor.getAttribute("data-data")));
      dataValues.forEach((item) => {
        if (item) {
          let z = {
            noteId: note.id,
            objId: item.objId,
            objName: item.name,
            relType: item.type,
          };
          obj.noteObjs.push(z);
        }
      });
      obj[key] = value;
    });
    selfUpdateNoteInfo(obj);
  };

  const handleItemClick = (id: string) => {
    setModifyItem(SET_PLACE_TOP_ID, id);
  };
  const items: MenuProps["items"] = [
    {
      key: "1",
      label: <span className="childMenuDropdown">保存到便签组</span>,
      onClick: (event) => noteSiderClick(event, "group"),
    },
    {
      key: "2",
      label: <span className="childMenuDropdown">新增组</span>,
      onClick: (event) => noteSiderClick(event, "addGroup"),
    },
  ];
  return (
    <div
      style={{
        ...style,
        visibility: visibility,
        zIndex: noteInfo.id === placeTop ? 2147483647 : 2147483646,
      }}
      className={classNames("sino-customize-tooltip show", isThumbnail ? "isThumbnail" : "")}
      id={`note${isThumbnail ? "isThumbnail" : ""}${noteInfo.id}`}
      data-x={noteInfo.noteStyle.noteLeft}
      data-y={noteInfo.noteStyle.noteTop}
      onClick={() => handleItemClick(noteInfo.id)}
    >
      {isThumbnail && !noteInfo?.promptId && (
        <div
          style={{
            position: "absolute",
            width: "40px",
            height: "40px",
            left: "50%",
            top: "50%",
            marginTop: "-20px",
            marginLeft: "-20px",
            visibility: visibility === "hidden" ? "visible" : "hidden",
          }}
          title="0"
        >
          <svg
            version="1.1"
            id="loader-1"
            xmlns="http://www.w3.org/2000/svg"
            x="0px"
            y="0px"
            width="40px"
            height="40px"
            viewBox="0 0 40 40"
            enableBackground="new 0 0 40 40"
          >
            <path
              opacity="0.2"
              fill="#1677ff"
              d="M20.201,5.169c-8.254,0-14.946,6.692-14.946,14.946c0,8.255,6.692,14.946,14.946,14.946
    s14.946-6.691,14.946-14.946C35.146,11.861,28.455,5.169,20.201,5.169z M20.201,31.749c-6.425,0-11.634-5.208-11.634-11.634
    c0-6.425,5.209-11.634,11.634-11.634c6.425,0,11.633,5.209,11.633,11.634C31.834,26.541,26.626,31.749,20.201,31.749z"
            />
            <path
              fill="#1677ff"
              d="M26.013,10.047l1.654-2.866c-2.198-1.272-4.743-2.012-7.466-2.012h0v3.312h0
    C22.32,8.481,24.301,9.057,26.013,10.047z"
            >
              <animateTransform
                attributeType="xml"
                attributeName="transform"
                type="rotate"
                from="0 20 20"
                to="360 20 20"
                dur="0.5s"
                repeatCount="indefinite"
              />
            </path>
          </svg>
        </div>
      )}
      {!isThumbnail && (
        <Flex
          className={classNames(NOTE_PANEL_NAME, {
            opactive: noteInfo.type === QUIZ || noteInfo.type === AI,
          })}
        >
          <NotePin
            note={noteInfo}
            isDropdownOpen={isDropdownOpen}
            onExpanded={() => changeExpanded(false)}
            onFixed={changeFixed}
          />
        </Flex>
      )}
      <Flex
        vertical
        style={{
          position: "relative",
        }}
      >
        {(noteInfo.type === PLAIN || noteInfo.type === QUOTE || noteInfo.type === ONQUIZ) && (
          <PlainNote
            note={noteInfo}
            updateNoteValue={updateNoteValue}
            isThumbnail={isThumbnail}
            acitve={noteInfo.id === placeTop || isHover}
            onExpanded={() => changeExpanded(true)}
          />
        )}
        {(noteInfo.type === AI || noteInfo.type === QUIZ) && (
          <NoteIns
            note={noteInfo}
            themeBgcColor={noteInfo.color}
            isThumbnail={isThumbnail}
            acitve={noteInfo.id === placeTop || isHover}
            onExpanded={() => changeExpanded(true)}
          />
        )}
        {/* 目前判断的是有没有指令 */}
        {!isThumbnail && noteInfo.type !== AI && noteInfo.type !== QUIZ && (
          <>
            <div
              style={{
                display: noteInfo.id === placeTop ? "block" : "none",
                right: "0px",
                position: "absolute",
                top: "-36px",
                paddingBottom: "3px",
                zIndex: 10,
              }}
            >
              <Flex
                style={{
                  padding: `${token.paddingXXS}px ${token.paddingXXS * 2}px`,
                  borderRadius: token.borderRadius,
                  backgroundColor: noteInfo?.color,
                  boxShadow: "0px 3px 6px -4px rgba(0, 0, 0, 0.12)",
                  border: `1px solid ${token.colorBorder}`,
                }}
                gap={token.paddingXXS}
              >
                <Tooltip
                  placement="top"
                  title="新增便签组"
                  getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                >
                  <Button
                    icon={<MedicineBoxOutlined width={14} height={14} />}
                    onClick={(event) => noteSiderClick(event, "addGroup")}
                    style={{ padding: token.paddingXXS, height: "24px", width: "24px" }}
                    type="text"
                  ></Button>
                </Tooltip>
                {/* <Dropdown
                  menu={{ items }}
                  placement="top"
                  getPopupContainer={(triggerNode) => triggerNode.parentNode.parentNode as any}
                >
                  <Button
                    icon={<MedicineBoxOutlined width={14} height={14} />}
                    onClick={(event) => noteSiderClick(event, "group")}
                    style={{ padding: token.paddingXXS, height: "24px", width: "24px" }}
                    type="text"
                  ></Button>
                </Dropdown> */}

                <Tooltip
                  placement="top"
                  title="查看评论"
                  getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                >
                  <Button
                    icon={<InfoSVGIcon width={14} height={14} />}
                    style={{ padding: token.paddingXXS, height: "24px", width: "24px" }}
                    onClick={(event) => noteSiderClick(event, "info")}
                    type="text"
                  ></Button>
                </Tooltip>

                <Tooltip
                  placement="top"
                  title="复制"
                  getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                >
                  <Button
                    icon={<CopySVGIcon width={14} height={14} />}
                    style={{ padding: token.paddingXXS, height: "24px", width: "24px" }}
                    onClick={(event) => noteSiderClick(event, "copy")}
                    type="text"
                  ></Button>
                </Tooltip>
                <Tooltip
                  placement="top"
                  title="分享"
                  getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                >
                  <Button
                    icon={<CooperationSVGIcon width={14} height={14} />}
                    style={{ padding: token.paddingXXS, height: "24px", width: "24px" }}
                    type="text"
                    onClick={(event) => noteSiderClick(event, "cooperation")}
                  ></Button>
                </Tooltip>
                <Tooltip
                  placement="top"
                  title="知识库"
                  getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                >
                  <Button
                    icon={<IconFont type={"knowledgeBaseOutlined"} />}
                    style={{ padding: token.paddingXXS, height: "24px", width: "24px" }}
                    type="text"
                    onClick={(event) => noteSiderClick(event, "knowlwdge")}
                  ></Button>
                  {/* <Button
                      icon={<IconFont type={"knowledgeBaseOutlined"} style={{ fill: token.colorTextTertiary }} />}
                      type="text"
                      style={{ padding: token.paddingXXS, height: "20px", width: "20px" }}
                      onClick={showModal}
                    ></Button> */}
                </Tooltip>
                <Tooltip
                  placement="top"
                  title="换肤"
                  getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                >
                  <Button
                    icon={<ThemeSVGIcon width={14} height={14} />}
                    style={{ padding: token.paddingXXS, height: "24px", width: "24px" }}
                    type="text"
                    onClick={(event) => noteSiderClick(event, "theme")}
                    disabled={!noteInfo.editable}
                  ></Button>
                </Tooltip>
                <Delete note={noteInfo} width={24} svgWidth={14} />
              </Flex>
              <div
                className={`${themePanelTop > 0 ? "pos-top" : ""}`}
                style={{
                  position: "absolute",
                  top: themePanelTop + "px",
                  right: "-14px",
                  display: barKey == "theme" ? "block" : "none",
                }}
              >
                <ThemePicker note={noteInfo} onChange={handleThemeBack} />
              </div>
            </div>
            <div
              className={NOTE_CONFIG_PANEL}
              style={{
                top: "0px",
                [configPos]: noteInfo.noteStyle.noteWidth || 344 + "px",
              }}
            >
              {barKey == "cooperation" && <CopyUrl note={noteInfo} />}
              {barKey == "info" && <Comment noteInfo={noteInfo} handleBack={handleCooperation} />}
            </div>
          </>
        )}
      </Flex>
      <NoteSelectGroupModal
        visible={groupVisible}
        noteId={noteInfo.id}
        onClose={() => {
          setGroupVisible(false);
          setBarKey("");
        }}
        onConfirm={() => {
          setBarKey("");
          setGroupVisible(false);
        }}
      />
      <NoteGroupModal
        visible={addGroupVisible}
        position="window"
        onClose={() => {
          setAddGroupVisible(false);
          setBarKey("");
        }}
        onConfirm={() => {
          setBarKey("");
          setAddGroupVisible(false);
        }}
      />
      <NoteKnowledgeModal
        visible={modalVisible}
        noteInfo={noteInfo}
        onClose={() => {
          setModalVisible(false);
        }}
        onConfirm={() => {
          setModalVisible(false);
        }}
      />
    </div>
  );
};

export default NoteItem;
